<?php
// +----------------------------------------------------------------------
// | 星辰小牛Admin
// +----------------------------------------------------------------------
// | Website: ***.***.***
// +----------------------------------------------------------------------
// | Author: dav <***.***.***>
// +----------------------------------------------------------------------

namespace app\api\controller;
use app\common\controller\ApiBase;
use app\api\middleware\Jwt;
use thans\jwt\facade\JWTAuth;
use app\common\model\Member as MemberModel;
use app\common\model\MemberCouponList as MemberCouponListModel;
use app\common\model\Recharge as RechargeModel;
use app\common\model\BlindBox as BlindBoxModel;
use app\common\model\BlindBoxBuy as BlindBoxBuyModel;
use app\common\model\MemberAddress as MemberAddressModel;
use app\common\model\Goods as GoodsModel;
use app\common\model\BlindBoxOrder as BlindBoxOrderModel;
class Pay extends ApiBase
{
    /**
     * 控制器中间件 [登录、注册 不需要鉴权]
     * @var array
     */
	protected $middleware = [
    	Jwt::class => ['except' 	=> ['go_epay'] ]
    ];
    
    //查询订单是否已完成支付
    public function get_recharge_status(){
        $RechargeModel = new RechargeModel;
        $order_no = input('order_no');
        $status = $RechargeModel->where('order_no',$order_no)->value('pay_status');
        return json(['code'=>1,'order_no'=>$order_no,'status'=>$status]);
    }
    
    //请求商城支付
    public function go_goods_pay(){
        $MemberModel = new MemberModel;
        $GoodsModel = new GoodsModel;
        $RechargeModel = new RechargeModel;
        $MemberAddressModel = new MemberAddressModel;
        $goods = $GoodsModel->get_find([ ['id','=',input('goods_id',0,'int')] ]);
        if(!$goods){
            $this->error('商品不存在');
        }
        if($goods['stock']<=0){
            $this->error('该产品库存不足！');
        }
        $member = $MemberModel->find($this->get_uid()); //查询购买用户
        if(!$member || $member['status']!=1){
            $this->success('未登录','','',401);
        }
        if($goods['goods_type']==1){ //实物商品
            $my_address = $MemberAddressModel->find(input('address_id'));
            if(!$my_address || $my_address['uid']!=$this->get_uid()){
                $this->error('收货地址数据错误');
            }
        }
        $post = input();
        if(input('paymode')=='integral'){ //积分支付
            $pay_price = $goods['integral'] * input('num');
            if($pay_price<0){
                $this->error('系统价值配置错误，请联系管理员！');
            }
            if($member['integral']<$pay_price){
                $this->error('您的幸运币余额不足');
            }
        }else{ //在线支付
            $pay_price = $goods['price'] * input('num');
            
        }
        
        $pay_price = $pay_price + $goods['freight_value']; //把统计好的价格加上运费
        
        if(input('paymode')=='wechat'){ //客户端传来的支付方式是微信支付
            if(input('client')==0 && xn_cfg('general_website.h5_wxpay_type')==0){ //H5并且支付模式是易支付
                $pay_method = 4;
                $notify_url = get_host_url().'/api/notify/recharge_epay_notify';
            }
            if(input('client')==0 && xn_cfg('general_website.h5_wxpay_type')==1){ //H5并且支付模式是官方支付
                $pay_method = 7;
                $notify_url = get_host_url().'/api/notify/recharge_h5wxpay_notify';
            }
            if(input('client')==1){ //小程序
                $pay_method = 1;
                $notify_url = get_host_url().'/api/notify/recharge_amp_wxpay_notify';
            }
            if(input('client')==2){ //APP
                $pay_method = 3;
                $notify_url = get_host_url().'/api/notify/recharge_app_wxpay_notify';
            }
        }else if(input('paymode')=='alipay'){ //客户端传来的支付方式是支付宝支付
            if(input('client')==0 && xn_cfg('general_website.h5_alipay_type')==0){ //H5并且支付模式是易支付
                $pay_method = 5;
                $notify_url = get_host_url().'/api/notify/recharge_epay_notify';
            }
            if(input('client')==0 && xn_cfg('general_website.h5_alipay_type')==1){ //H5并且支付模式是官方支付
                $pay_method = 8;
                $notify_url = get_host_url().'/api/notify/recharge_h5alipay_notify';
            }
            if(input('client')==2){ //APP
                $pay_method = 2;
                $notify_url = get_host_url().'/api/notify/recharge_app_alipay_notify';
            }
        }else if(input('paymode')=='paypal'){
            $pay_method = 9;
            $notify_url = get_host_url().'/api/notify/recharge_paypal_notify';
        }else if(input('paymode')=='ottpay'){
            $pay_method = 10;
            $notify_url = get_host_url().'/api/notify/recharge_ottpay_notify';
        }else if(input('paymode')=='integral'){ //客户端传来的支付方式是积分支付
            $pay_method = 6;
        }else if(input('paymode')=='baxipay'){
            $pay_method = 11;
            $notify_url = get_host_url().'/api/notify/recharge_baxipay_notify';
        }else if(input('paymode')=='hwdzpay'){
            $pay_method = 12;
            $notify_url = get_host_url().'/api/notify/recharge_hwdzpay_notify';
        }else if(input('paymode')=='hzpay'){
            $pay_method = 13;
            $notify_url = get_host_url().'/api/notify/recharge_hzpay_notify';
        }
        $add_log = [
            'order_no'=>get_order('Sc-'),
            'uid'=>$this->get_uid(),
            'client'=>input('client'),
            'original_price'=>$pay_price,
            'offer_remarks'=>'',
            'price'=>$pay_price,
            'pay_method'=>$pay_method,
            'type'=>1,
            'post_data'=>$post,
        ];
        $RechargeModel = new RechargeModel;
        $res = $RechargeModel->save($add_log); //写入订单
        if(!$res){
            $this->error('订单写入失败，请重试');
        }
        $add_log['id'] = $RechargeModel->id;
        //一切就绪，拉起支付
        $ClassName = '\app\common\lib\Pay\\'.$RechargeModel->get_pay_method_notify_class()[$pay_method];
        $PayClass = new $ClassName;
        if(!method_exists($PayClass,'goPay')){
            $this->error('支付方式错误，联系客服咨询！');
        }
        $add_log['pay_title'] = '在线购买商品支付';
        $add_log['notify_url'] = $notify_url;
        $add_log['member_wx_mini_openid'] = $member['wx_mini_openid'];
        $add_log['wx_app_openid'] = $member['wx_app_openid'];
        $add_log['wx_h5_openid'] = $member['wx_h5_openid'];
        
        try {
            $pay_res = $PayClass->goPay($add_log,$this->app);
            
        } catch (ValidateException $e) {
            // 这是进行验证异常捕获
            $this->error($e->getError());
        } catch (\Exception $e) {
            // 这是进行异常捕获
            $this->error($e->getMessage());
        }
        if(input('paymode')=='integral'){ //积分支付
            $this->success('订单请求成功',$pay_res,'',8);
        }
        
        $this->success('订单请求成功',$pay_res);
        
    }
    
    //获取盲盒支付价格
    public function get_box_price(){
        $MemberModel = new MemberModel;
        $member = $MemberModel->find($this->get_uid()); //查询购买用户
        if(!$member || $member['status']!=1){
            $this->success('未登录','','',401);
        }
        $post = input();
        $blind_box_id = input('boxid',0,'int');
        $BlindBoxModel = new BlindBoxModel;
        $blind_box = $BlindBoxModel->find($blind_box_id);
        if(!$blind_box || $blind_box['status']!=1){
            $this->error('盒子不存在，请检查！');
        }
        
        $BlindBoxBuyModel = new BlindBoxBuyModel;
        $buy_fangan = $BlindBoxBuyModel->where('number',input('num',0,'int'))->find();
        if(!$buy_fangan || $buy_fangan['status']!=1){ //购买方案不存在
            $this->error('购买方案不存在');
        }
        $RechargeModel = new RechargeModel;
        $box_price_data = $RechargeModel->get_box_price(
            $blind_box,
            $member,
            $buy_fangan,
            $post
        ); //获取支付价格
        //var_dump($box_price_data);die;
        $pay_price = $box_price_data['pay_price'];
        $xs_dk_num = $box_price_data['xingshi'];
        $xs_dk_price = $box_price_data['xingshi'] / $blind_box['integral2_dikou_rmb'];
        $offer_remarks = $box_price_data['offer_remarks'];
        if($xs_dk_price>0){
            $xs_dk_price=$xs_dk_price;
        }else{
            $xs_dk_price=0;
        }
        $arr=['price'=>$pay_price,'xs_dk_num'=>$xs_dk_num,'xs_dk_price'=>$xs_dk_price,'offer_remarks'=>$offer_remarks];
       // var_dump($xs_dk_price);
        $this->success('ok',$arr);
    }
    
    //请求盲盒支付
    public function go_pay(){
        $MemberModel = new MemberModel;
        $member = $MemberModel->find($this->get_uid()); //查询购买用户
        if(!$member || $member['status']!=1){
            $this->success('未登录','','',401);
        }
        $post = input();
        $blind_box_id = input('boxid',0,'int');
        $BlindBoxModel = new BlindBoxModel;
        $blind_box = $BlindBoxModel->find($blind_box_id);
        if(!$blind_box){
            $this->error('盒子不存在，请检查！');
        }
        // var_dump($blind_box);die;
        if($blind_box['is_xrmf']==1){
            $open_box_count = BlindBoxOrderModel::where('uid',$this->get_uid())->where('blind_box_id',$blind_box['id'])->where('status',1)->count(); //获取用户已开盒数量
            if($open_box_count>0){
                $this->error('新人免费盒子每人仅限抽取一次');
            }
        }
        
        $BlindBoxBuyModel = new BlindBoxBuyModel;
        $buy_fangan = $BlindBoxBuyModel->where('number',input('num',0,'int'))->find();
        if(!$buy_fangan || $buy_fangan['status']!=1){ //购买方案不存在
            $this->error('购买方案不存在');
        }
        $RechargeModel = new RechargeModel;
        $box_price_data = $RechargeModel->get_box_price(
            $blind_box,
            $member,
            $buy_fangan,
            $post,
        ); //获取支付价格
        $pay_price = $box_price_data['pay_price'];
        $offer_remarks = $box_price_data['offer_remarks'];
        $post['xs'] = $box_price_data['xingshi'];
        // var_dump($box_price_data);die;
        
        if(input('paymode')=='wechat'){ //客户端传来的支付方式是微信支付
            if(input('client')==0 && xn_cfg('general_website.h5_wxpay_type')==0){ //H5并且支付模式是易支付
                $pay_method = 4;
                $notify_url = get_host_url().'/api/notify/recharge_epay_notify';
            }
            if(input('client')==0 && xn_cfg('general_website.h5_wxpay_type')==1){ //H5并且支付模式是官方支付
                $pay_method = 7;
                $notify_url = get_host_url().'/api/notify/recharge_h5wxpay_notify';
            }
            if(input('client')==1){ //小程序
                $pay_method = 1;
                $notify_url = get_host_url().'/api/notify/recharge_amp_wxpay_notify';
            }
            if(input('client')==2){ //APP
                $pay_method = 3;
                $notify_url = get_host_url().'/api/notify/recharge_app_wxpay_notify';
            }
        }else if(input('paymode')=='alipay'){ //客户端传来的支付方式是支付宝支付
            if(input('client')==0 && xn_cfg('general_website.h5_alipay_type')==0){ //H5并且支付模式是易支付
                $pay_method = 5;
                $notify_url = get_host_url().'/api/notify/recharge_epay_notify';
            }
            if(input('client')==0 && xn_cfg('general_website.h5_alipay_type')==1){ //H5并且支付模式是易支付
                $pay_method = 8;
                $notify_url = get_host_url().'/api/notify/recharge_h5alipay_notify';
            }
            if(input('client')==2){ //APP
                $pay_method = 2;
                $notify_url = get_host_url().'/api/notify/recharge_app_alipay_notify';
            }
        }else if(input('paymode')=='paypal'){
            $pay_method = 9;
            $notify_url = get_host_url().'/api/notify/recharge_paypal_notify';
        }else if(input('paymode')=='ottpay'){
            $pay_method = 10;
            $notify_url = get_host_url().'/api/notify/recharge_ottpay_notify';
        }else if(input('paymode')=='integral'){ //客户端传来的支付方式是积分支付
            $pay_method = 6;
            if($blind_box['integral_price']===null){
                $this->error('该产品幸运币价格异常，联系客服检查！');
            }
        }else if(input('paymode')=='baxipay'){
            $pay_method = 11;
            $notify_url = get_host_url().'/api/notify/recharge_baxipay_notify';
        }else if(input('paymode')=='hwdzpay'){
            $pay_method = 12;
            $notify_url = get_host_url().'/api/notify/recharge_hwdzpay_notify';
        }else if(input('paymode')=='hzpay'){
            $pay_method = 13;
            $notify_url = get_host_url().'/api/notify/recharge_hzpay_notify';
        }
        $add_log = [
            'order_no'=>get_order('Mh-'),
            'uid'=>$this->get_uid(),
            'client'=>input('client'),
            'original_price'=>$blind_box['price'] * input('num'),
            'offer_remarks'=>$offer_remarks,
            'price'=>$pay_price,
            'pay_method'=>$pay_method,
            'type'=>2,
            'post_data'=>$post,
        ];
        $res = $RechargeModel->save($add_log); //写入订单
        if(!$res){
            $this->error('订单写入失败，请重试');
        }
        $add_log['id'] = $RechargeModel->id;
        if($pay_price==0){ //如果实际支付价格为0，那么直接修改订单信息并执行回调
            $RechargeModel->where('order_no',$add_log['order_no'])->save([
                'pay_status'=>1,
                'pay_time'=>time(),
            ]);
            $Notify  = new Notify($this->app);
            $huitiao_id = $Notify->add_blind_box_order($add_log);
            if($huitiao_id!=false){
                $data = [
                    'ooid'=>$add_log['order_no'],
                ];
                $this->success('支付成功',$data,'',8);
            }else{
                $this->error('订单状态修改失败');
            }
        }
        
        //一切就绪，拉起支付
        $ClassName = '\app\common\lib\Pay\\'.$RechargeModel->get_pay_method_notify_class()[$pay_method];
        $PayClass = new $ClassName;
        if(!method_exists($PayClass,'goPay')){
            $this->error('支付方式错误，联系客服咨询！');
        }
        $add_log['pay_title'] = '盲盒开盒';
        $add_log['notify_url'] = $notify_url;
        $add_log['member_wx_mini_openid'] = $member['wx_mini_openid'];
        $add_log['wx_app_openid'] = $member['wx_app_openid'];
        $add_log['wx_h5_openid'] = $member['wx_h5_openid'];
        
        try {
            $pay_res = $PayClass->goPay($add_log,$this->app);
        } catch (ValidateException $e) {
            // 这是进行验证异常捕获
            $this->error($e->getError());
        } catch (\Exception $e) {
            // 这是进行异常捕获
            $this->error($e->getMessage());
        }
        if(input('paymode')=='integral'){ //积分支付
            $data = [
                'ooid'=>$add_log['order_no'],
            ];
            $this->success('订单请求成功',$data,'',8);
        }
        
        $this->success('订单请求成功',$pay_res);
    }
    
    /**
     * 支付提货运费
     * $post 用户提交的参数
     * $ids 提货IDS
     * $freight_value 运费金额
     * $uid 用户ID
     **/
    function pay_freight_value($post,$ids,$freight_value,$uid){
        $MemberModel = new MemberModel;
        $member = $MemberModel->find($uid); //查询购买用户
        
        if($post['paymode']=='wechat'){ //客户端传来的支付方式是微信支付
            if(input('client')==0 && xn_cfg('general_website.h5_wxpay_type')==0){ //H5并且支付模式是易支付
                $pay_method = 4;
                $notify_url = get_host_url().'/api/notify/recharge_epay_notify';
            }
            if(input('client')==0 && xn_cfg('general_website.h5_wxpay_type')==1){ //H5并且支付模式是官方支付
                $pay_method = 7;
                $notify_url = get_host_url().'/api/notify/recharge_h5wxpay_notify';
            }
            if($post['client']==1){ //小程序
                $pay_method = 1;
                $notify_url = get_host_url().'/api/notify/recharge_amp_wxpay_notify';
            }
            if($post['client']==2){ //APP
                $pay_method = 3;
                $notify_url = get_host_url().'/api/notify/recharge_app_wxpay_notify';
            }
        }else if($post['paymode']=='alipay'){ //客户端传来的支付方式是支付宝支付
            if(input('client')==0 && xn_cfg('general_website.h5_alipay_type')==0){ //H5并且支付模式是易支付
                $pay_method = 5;
                $notify_url = get_host_url().'/api/notify/recharge_epay_notify';
            }
            if(input('client')==0 && xn_cfg('general_website.h5_alipay_type')==1){ //H5并且支付模式是易支付
                $pay_method = 8;
                $notify_url = get_host_url().'/api/notify/recharge_h5alipay_notify';
            }
            if($post['client']==2){ //APP
                $pay_method = 2;
                $notify_url = get_host_url().'/api/notify/recharge_app_alipay_notify';
            }
        }else if(input('paymode')=='paypal'){
            $pay_method = 13;
            $notify_url = get_host_url().'/api/notify/recharge_hzpay_notify';
        }else if(input('paymode')=='ottpay'){
            $pay_method = 10;
            $notify_url = get_host_url().'/api/notify/recharge_ottpay_notify';
        }else if(input('paymode')=='baxipay'){
            $pay_method = 11;
            $notify_url = get_host_url().'/api/notify/recharge_baxipay_notify';
        }
        // var_dump($post);die;
        $add_log = [
            'order_no'=>get_order('Freight-'),
            'uid'=>$uid,
            'client'=>$post['client'],
            'original_price'=>$freight_value,
            'offer_remarks'=>'',
            'price'=>$freight_value,
            'pay_method'=>$pay_method,
            'type'=>3,
            'post_data'=>$post,
        ];
        $RechargeModel = new RechargeModel;
        $res = $RechargeModel->save($add_log); //写入订单
        if(!$res){
            $this->error('订单写入失败，请重试');
        }
        $add_log['id'] = $RechargeModel->id;
        
        //一切就绪，拉起支付
        $ClassName = '\app\common\lib\Pay\\'.$RechargeModel->get_pay_method_notify_class()[$pay_method];
        $PayClass = new $ClassName;
        if(!method_exists($PayClass,'goPay')){
            $this->error('支付方式错误，联系客服咨询！');
        }
        $add_log['pay_title'] = '运费支付';
        $add_log['notify_url'] = $notify_url;
        $add_log['member_wx_mini_openid'] = $member['wx_mini_openid'];
        $add_log['wx_app_openid'] = $member['wx_app_openid'];
        $add_log['wx_h5_openid'] = $member['wx_h5_openid'];
        
        try {
            $pay_res = $PayClass->goPay($add_log);
        } catch (ValidateException $e) {
            // 这是进行验证异常捕获
            $this->error($e->getError());
        } catch (\Exception $e) {
            // 这是进行异常捕获
            $this->error($e->getMessage());
        }
        
        $this->success('订单请求成功',$pay_res);
    }
    
    //易支付跳转
    public function go_epay(){
        $order_no = input('order_no','');
        $RechargeModel = new RechargeModel;
        $order = $RechargeModel->where('order_no',$order_no)->find();
        if(!$order){
            $this->error('订单不存在！');
        }
        $order['pay_title'] = '盲盒开盒';
        $order['notify_url'] = get_host_url().'/api/notify/recharge_epay_notify';
        $order['tz_url'] = xn_cfg('general_website.h5_url').'/#/pages/home/<USER>'.$order['order_no'].'&num='.$order['post_data']['num'].'&boxid='.$order['post_data']['boxid'];
        $order['return_url'] = get_host_url().'/api/notify/recharge_epay_return';
        
                if($order['pay_method']==5 && (strpos($_SERVER['HTTP_USER_AGENT'], 'QQ/')||strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger')!==false)){
    $siteurl=get_host_url().'/api/pay/go_epay?order_no='.input('order_no');
    $return_url = $order['tz_url'];
echo '<html>
<head>
    <meta charset="UTF-8">
    <title>使用浏览器打开</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta name="format-detection" content="telephone=no">
    <meta content="false" name="twcClient" id="twcClient">
    <meta name="aplus-touch" content="1">
    <style>
        body,html{width:100%;height:100%}
        *{margin:0;padding:0}
        body{background-color:#fff;background:url(/static/index/images/tzbg.jpg);background-size:100% 100%;}
    </style>
</head>
<body>
<a href="'.$return_url.'">
<div style="width:100%;background-color:#ff5722;color:#fff;text-align:center;padding:20px 0;position: fixed;bottom: 0;">
    支付完成后<span style="color:#fff;background-color:#00e609;padding:1px 5px;margin:0 2px;">√</span>点我开盒
</div>
</a>
</body>
</html>';
exit;
}
        $pay_method = $order['pay_method'];
        $ClassName = '\app\common\lib\Pay\\'.$RechargeModel->get_pay_method_notify_class()[$pay_method];
        $PayClass = new $ClassName;
        if(!method_exists($PayClass,'go_epay')){
            $this->error('支付方式错误，联系客服咨询！');
        }
        $PayClass->go_epay($order);
    }
    
}