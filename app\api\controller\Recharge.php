<?php
// +----------------------------------------------------------------------
// | 星辰小牛Admin
// +----------------------------------------------------------------------
// | Website: ***.***.***
// +----------------------------------------------------------------------
// | Author: dav <***.***.***>
// +----------------------------------------------------------------------

namespace app\api\controller;

use app\common\controller\ApiBase;
use app\api\middleware\Jwt;
use thans\jwt\facade\JWTAuth;
use app\common\model\Member as MemberModel;
use app\common\model\Recharge as RechargeModel;
use think\facade\Log;
use think\exception\ValidateException;
class Recharge extends ApiBase
{
    /**
     * 控制器中间件 [需要登录验证]
     * @var array
     */
    protected $middleware = [
        Jwt::class => ['except' => ['notify_hzpay', 'test']]
    ];

    /**
     * 测试方法
     */
    public function test()
    {
        return json(['code' => 1, 'msg' => '充值控制器测试成功', 'data' => ['time' => date('Y-m-d H:i:s')]]);
    }

    /**
     * 创建充值订单
     */
    public function create_order()
    {
        try {
            $amount = input('post.amount', 0, 'float');
            $pay_method = input('post.pay_method', 13, 'int'); // 默认使用HzPay
            $client = input('post.client', 2, 'int'); // 客户端类型：0=web, 1=微信小程序, 2=app

            // 参数验证
            if ($amount <= 0) {
                $this->error('充值金额必须大于0');
            }

            // 获取充值配置
            $min_amount = (float)xn_cfg('general_website.min_recharge_amount') ?: 1;
            $max_amount = (float)xn_cfg('general_website.max_recharge_amount') ?: 10000;

            if ($amount < $min_amount) {
                $this->error('充值金额不能少于' . $min_amount . '元');
            }

            if ($amount > $max_amount) {
                $this->error('充值金额不能超过' . $max_amount . '元');
            }

            // 检查支付方式是否支持
            $RechargeModel = new RechargeModel;
            $pay_methods = $RechargeModel->get_pay_method_notify_class();
            if (!isset($pay_methods[$pay_method])) {
                $this->error('不支持的支付方式');
            }

            // 获取用户信息
            $uid = $this->get_uid();
            $MemberModel = new MemberModel;
            $member = $MemberModel->find($uid);
            if (!$member) {
                $this->error('用户信息不存在');
            }

            // 生成回调地址
            $notify_url = get_host_url() . '/api/recharge/notify_hzpay';

            // 创建充值订单
            $order_data = [
                'order_no' => get_order('CZ-'),
                'uid' => $uid,
                'client' => $client,
                'original_price' => $amount,
                'offer_remarks' => '',
                'price' => $amount,
                'pay_method' => $pay_method,
                'type' => 4, // 充值类型
                'post_data' => json_encode([
                    'amount' => $amount,
                    'pay_method' => $pay_method,
                    'client' => $client
                ]),
            ];

            $res = $RechargeModel->save($order_data);
            if (!$res) {
                $this->error('订单创建失败，请重试');
            }

            $order_data['id'] = $RechargeModel->id;

            // 处理手机号格式
            $phone = $member['mobile'] ?: '13800138000';
            // 确保手机号是有效格式（11位数字）
            if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
                $phone = '13800138000'; // 使用默认手机号
            }

            // 准备支付参数
            $pay_data = [
                'order_no' => $order_data['order_no'],
                'price' => $amount,
                'pay_title' => '账户充值',
                'notify_url' => $notify_url,
                'username' => $member['nickname'] ?: $member['username'],
                'phone' => $phone,
                'email' => $member['mobile'] ? $member['mobile'] . '@example.com' : '<EMAIL>',
                'member_wx_mini_openid' => $member['wx_mini_openid'] ?? '',
                'wx_app_openid' => $member['wx_app_openid'] ?? '',
                'wx_h5_openid' => $member['wx_h5_openid'] ?? '',
            ];

            // 记录支付参数日志
            Log::info('HzPay支付参数', [
                'order_no' => $order_data['order_no'],
                'pay_data' => $pay_data
            ]);

            // 调用支付类
            $ClassName = '\app\common\lib\Pay\\' . $pay_methods[$pay_method];
            $PayClass = new $ClassName;

            if (!method_exists($PayClass, 'goPay')) {
                $this->error('支付方式配置错误，请联系客服');
            }

            $pay_result = $PayClass->goPay($pay_data, $this->app);

            // 记录日志
            Log::info('充值订单创建成功', [
                'order_no' => $order_data['order_no'],
                'uid' => $uid,
                'amount' => $amount,
                'pay_method' => $pay_method
            ]);

            $this->success('订单创建成功', [
                'order_no' => $order_data['order_no'],
                'order_id' => $order_data['id'],
                'amount' => $amount,
                'pay_url' => $pay_result,
                'pay_method' => $pay_method,
                'pay_method_name' => $RechargeModel->get_pay_method_list()[$pay_method] ?? 'HzPay支付'
            ]);

        } catch (ValidateException $e) {
            Log::error('充值支付验证异常', [
                'error' => $e->getError()
            ]);
            $this->error($e->getError());
        } catch (\Exception $e) {
            Log::error('充值支付异常', [
                'error' => $e->getMessage()
            ]);
            $this->error('支付请求失败：' . $e->getMessage());
        }
    }

    /**
     * 查询充值订单状态
     */
    public function query_order()
    {
        $order_no = input('post.order_no', '', 'trim');
        
        if (empty($order_no)) {
            $this->error('订单号不能为空');
        }

        $uid = $this->get_uid();
        $RechargeModel = new RechargeModel;
        
        $order = $RechargeModel->where('order_no', $order_no)
                              ->where('uid', $uid)
                              ->where('type', 4)
                              ->find();

        if (!$order) {
            $this->error('订单不存在');
        }

        $this->success('查询成功', [
            'order_no' => $order['order_no'],
            'amount' => $order['price'],
            'pay_status' => $order['pay_status'],
            'pay_status_text' => $order['pay_status_text'],
            'pay_method' => $order['pay_method'],
            'pay_method_text' => $order['pay_method_text'],
            'create_time' => $order['create_time'],
            'pay_time' => $order['pay_time'] ?? 0
        ]);
    }

    /**
     * 获取充值记录
     */
    public function get_recharge_list()
    {
        $page = input('get.page', 1, 'int');
        $limit = input('get.limit', 10, 'int');
        
        $uid = $this->get_uid();
        $RechargeModel = new RechargeModel;
        
        $where = [
            ['uid', '=', $uid],
            ['type', '=', 4] // 充值类型
        ];
        
        $list = $RechargeModel->where($where)
                             ->order('id desc')
                             ->page($page, $limit)
                             ->select();

        $total = $RechargeModel->where($where)->count();

        $data = [];
        foreach ($list as $item) {
            $data[] = [
                'id' => $item['id'],
                'order_no' => $item['order_no'],
                'amount' => $item['price'],
                'pay_status' => $item['pay_status'],
                'pay_status_text' => $item['pay_status_text'],
                'pay_method' => $item['pay_method'],
                'pay_method_text' => $item['pay_method_text'],
                'create_time' => $item['create_time'],
                'pay_time' => $item['pay_time'] ?? 0
            ];
        }

        $this->success('查询成功', [
            'list' => $data,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 获取充值配置
     */
    public function get_recharge_config()
    {
        $config = [
            'min_amount' => (float)(xn_cfg('general_website.min_recharge_amount') ?: 1),
            'max_amount' => (float)(xn_cfg('general_website.max_recharge_amount') ?: 10000),
            'hzpay_status' => xn_cfg('general_website.hzpay_status') ? true : false,
            'recharge_amounts' => [10, 50, 100, 200, 500, 1000], // 推荐充值金额
        ];

        $this->success('获取成功', $config);
    }

    /**
     * HzPay支付回调
     */
    public function notify_hzpay()
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        Log::info('HzPay充值回调', ['data' => $data]);

        if (!$data) {
            Log::error('HzPay充值回调数据解析失败', ['input' => $input]);
            echo 'FAIL';
            return;
        }

        try {
            // 验证签名
            $HzPay = new \app\common\lib\Pay\HzPay();
            if (!$HzPay->notify($data)) {
                Log::error('HzPay充值回调签名验证失败', ['data' => $data]);
                echo 'FAIL';
                return;
            }

            // 查找订单
            $order_no = $data['reference'] ?? '';
            if (empty($order_no)) {
                Log::error('HzPay充值回调缺少订单号', ['data' => $data]);
                echo 'FAIL';
                return;
            }

            $RechargeModel = new RechargeModel;
            $order = $RechargeModel->where('order_no', $order_no)
                                  ->where('type', 4)
                                  ->find();

            if (!$order) {
                Log::error('HzPay充值回调订单不存在', ['order_no' => $order_no]);
                echo 'FAIL';
                return;
            }

            // 检查订单状态
            if ($order['pay_status'] == 1) {
                Log::info('HzPay充值订单已处理', ['order_no' => $order_no]);
                echo 'SUCCESS';
                return;
            }

            // 检查支付状态
            if (isset($data['status']) && $data['status'] === 'success') {
                // 更新订单状态
                $RechargeModel->where('id', $order['id'])->update([
                    'pay_status' => 1,
                    'pay_time' => time(),
                    'pay_trade_no' => $data['transactionId'] ?? ''
                ]);

                // 增加用户余额
                $MemberModel = new MemberModel;
                $member = $MemberModel->find($order['uid']);
                if ($member) {
                    $member->balance = $member['balance'] + $order['price'];
                    $member->save();

                    Log::info('HzPay充值成功', [
                        'order_no' => $order_no,
                        'uid' => $order['uid'],
                        'amount' => $order['price'],
                        'new_balance' => $member['balance']
                    ]);
                }

                echo 'SUCCESS';
                return;
            }

            Log::error('HzPay充值支付失败', ['data' => $data]);
            echo 'FAIL';

        } catch (\Exception $e) {
            Log::error('HzPay充值回调处理异常', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            echo 'FAIL';
        }
    }
}
