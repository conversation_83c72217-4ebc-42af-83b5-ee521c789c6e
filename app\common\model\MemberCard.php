<?php
// +----------------------------------------------------------------------
// | 星辰小牛Admin
// +----------------------------------------------------------------------
// | Website: ***.***.***
// +----------------------------------------------------------------------
// | Author: dav <***.***.***>
// +----------------------------------------------------------------------

namespace app\common\model;
use think\facade\Db;
class MemberCard extends SetModel
{
    protected $autoWriteTimestamp = true;
    // 设置json类型字段
	protected $json = ['card_data'];
	// 设置JSON数据返回数组
    protected $jsonAssoc = true;
	// 追加字段
    protected $append = [
        'status_text',
    ];
    
    /**
     * 获取器 - 获取状态
     */
    public function getStatusTextAttr($value,$data){
        $status = $this->get_status_list();
        return $status[$data['status']];
    }
    
    public function get_status_list(){
        return [1=>'未使用',2=>'已使用'];
    }
    
    //添加用户卡片
    public function add_data($uid=0,$card_id=0,$remarks=''){
        $card = Card::find($card_id);;
        if(!$card){
            return false;
        }else{
            $card= $card->toArray();
        }
        $MemberModel = new Member;
        $member = $MemberModel->find($uid);
        if(!$member){
            return false;
        }
        $add_data = [
            'uid'=>$uid,
            'card_id'=>$card['id'],
            'status'=>1,
            'card_data'=>$card,
            'remarks'=>$remarks,
            
        ];
        $res = $this->save($add_data);
        if(!$res){
            return false;
        }
        return true;
    }
}