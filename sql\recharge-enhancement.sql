--
-- 充值功能增强SQL
-- Create by AI Assistant
--

-- 为充值订单表添加第三方交易号字段（如果不存在）
ALTER TABLE `xn_recharge` ADD COLUMN `pay_trade_no` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '第三方支付交易号' AFTER `pay_time`;

-- 为充值订单表添加索引
ALTER TABLE `xn_recharge` ADD INDEX `idx_type_uid` (`type`, `uid`);
ALTER TABLE `xn_recharge` ADD INDEX `idx_pay_trade_no` (`pay_trade_no`);

-- 创建充值配置表（可选，用于动态配置）
CREATE TABLE IF NOT EXISTS `xn_recharge_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(100) NOT NULL COMMENT '配置键',
  `value` text NOT NULL COMMENT '配置值',
  `description` varchar(255) DEFAULT '' COMMENT '配置描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1=启用，0=禁用',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值配置表';

-- 插入默认充值配置
INSERT INTO `xn_recharge_config` (`key`, `value`, `description`, `status`) VALUES
('min_amount', '1', '最小充值金额', 1),
('max_amount', '10000', '最大充值金额', 1),
('hzpay_enabled', '1', '是否启用HzPay支付', 1),
('recommend_amounts', '[10,50,100,200,500,1000]', '推荐充值金额（JSON格式）', 1)
ON DUPLICATE KEY UPDATE 
`value` = VALUES(`value`),
`description` = VALUES(`description`),
`update_time` = UNIX_TIMESTAMP();
