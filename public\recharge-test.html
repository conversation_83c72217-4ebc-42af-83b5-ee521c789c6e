<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充值功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, button { padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        input[type="number"], input[type="text"] { width: 200px; }
        button { background: #007bff; color: white; cursor: pointer; padding: 10px 20px; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .amount-buttons { margin: 10px 0; }
        .amount-btn { margin: 5px; padding: 8px 15px; background: #f8f9fa; border: 1px solid #dee2e6; cursor: pointer; }
        .amount-btn.active { background: #007bff; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>充值功能测试</h1>
        
        <!-- 登录区域 -->
        <div id="loginSection">
            <h3>1. 登录获取Token</h3>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="username" placeholder="请输入用户名">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="password" placeholder="请输入密码">
            </div>
            <button onclick="login()">登录</button>
        </div>

        <!-- 充值区域 -->
        <div id="rechargeSection" style="display: none;">
            <h3>2. 充值测试</h3>
            
            <!-- 获取配置 -->
            <div class="form-group">
                <button onclick="getConfig()">获取充值配置</button>
            </div>
            
            <!-- 充值金额选择 -->
            <div class="form-group">
                <label>选择充值金额:</label>
                <div class="amount-buttons" id="amountButtons">
                    <button class="amount-btn" onclick="selectAmount(10)">¥10</button>
                    <button class="amount-btn" onclick="selectAmount(50)">¥50</button>
                    <button class="amount-btn" onclick="selectAmount(100)">¥100</button>
                    <button class="amount-btn" onclick="selectAmount(200)">¥200</button>
                </div>
            </div>
            
            <div class="form-group">
                <label>自定义金额:</label>
                <input type="number" id="amount" placeholder="请输入充值金额" min="1" max="10000">
            </div>
            
            <div class="form-group">
                <label>支付方式:</label>
                <select id="payMethod">
                    <option value="13">HzPay支付</option>
                </select>
            </div>
            
            <div class="form-group">
                <button onclick="createOrder()">创建充值订单</button>
            </div>
            
            <!-- 查询订单 -->
            <div class="form-group">
                <label>订单号:</label>
                <input type="text" id="orderNo" placeholder="请输入订单号">
                <button onclick="queryOrder()">查询订单</button>
            </div>
            
            <!-- 获取充值记录 -->
            <div class="form-group">
                <button onclick="getRechargeList()">获取充值记录</button>
            </div>
        </div>

        <!-- 结果显示区域 -->
        <div id="result"></div>
    </div>

    <script>
        let token = localStorage.getItem('test_token') || '';
        let selectedAmount = 0;
        
        // 页面加载时检查token
        window.onload = function() {
            if (token) {
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('rechargeSection').style.display = 'block';
                showResult('已使用保存的Token', 'success');
            }
        };

        // 登录
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showResult('请输入用户名和密码', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/login/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 1) {
                    token = result.data.token;
                    localStorage.setItem('test_token', token);
                    document.getElementById('loginSection').style.display = 'none';
                    document.getElementById('rechargeSection').style.display = 'block';
                    showResult('登录成功！', 'success');
                } else {
                    showResult('登录失败: ' + result.msg, 'error');
                }
            } catch (error) {
                showResult('登录请求失败: ' + error.message, 'error');
            }
        }

        // 选择金额
        function selectAmount(amount) {
            selectedAmount = amount;
            document.getElementById('amount').value = amount;
            
            // 更新按钮状态
            document.querySelectorAll('.amount-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // 获取充值配置
        async function getConfig() {
            try {
                const response = await fetch('/api/recharge/get_recharge_config', {
                    headers: {
                        'Authorization': token
                    }
                });
                
                const result = await response.json();
                showResult('配置信息: ' + JSON.stringify(result, null, 2), result.code === 1 ? 'success' : 'error');
            } catch (error) {
                showResult('获取配置失败: ' + error.message, 'error');
            }
        }

        // 创建充值订单
        async function createOrder() {
            const amount = document.getElementById('amount').value;
            const payMethod = document.getElementById('payMethod').value;
            
            if (!amount || amount <= 0) {
                showResult('请输入有效的充值金额', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/recharge/create_order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token
                    },
                    body: JSON.stringify({
                        amount: parseFloat(amount),
                        pay_method: parseInt(payMethod),
                        client: 2
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 1) {
                    document.getElementById('orderNo').value = result.data.order_no;
                    showResult('订单创建成功: ' + JSON.stringify(result.data, null, 2), 'success');
                } else {
                    showResult('订单创建失败: ' + result.msg, 'error');
                }
            } catch (error) {
                showResult('创建订单失败: ' + error.message, 'error');
            }
        }

        // 查询订单
        async function queryOrder() {
            const orderNo = document.getElementById('orderNo').value;
            
            if (!orderNo) {
                showResult('请输入订单号', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/recharge/query_order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token
                    },
                    body: JSON.stringify({
                        order_no: orderNo
                    })
                });
                
                const result = await response.json();
                showResult('订单查询结果: ' + JSON.stringify(result, null, 2), result.code === 1 ? 'success' : 'error');
            } catch (error) {
                showResult('查询订单失败: ' + error.message, 'error');
            }
        }

        // 获取充值记录
        async function getRechargeList() {
            try {
                const response = await fetch('/api/recharge/get_recharge_list?page=1&limit=5', {
                    headers: {
                        'Authorization': token
                    }
                });
                
                const result = await response.json();
                showResult('充值记录: ' + JSON.stringify(result, null, 2), result.code === 1 ? 'success' : 'error');
            } catch (error) {
                showResult('获取充值记录失败: ' + error.message, 'error');
            }
        }

        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + type;
            resultDiv.innerHTML = '<pre>' + message + '</pre>';
        }
    </script>
</body>
</html>
