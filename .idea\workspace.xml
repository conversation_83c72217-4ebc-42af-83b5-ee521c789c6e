<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8840d444-5d02-47f4-940d-4a10ec0c79a9" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerConfigs">
    <option name="configs">
      <option value="$PROJECT_DIR$/bak/app/common/lib/Pay/SDK/WeChatDeveloper/composer.json" />
      <option value="$PROJECT_DIR$/app/bak/common/lib/Pay/SDK/WeChatDeveloper/composer.json" />
      <option value="$PROJECT_DIR$/app/common/lib/Pay/SDK/WeChatDeveloper/composer.json" />
      <option value="$PROJECT_DIR$/bak/composer.json" />
      <option value="$PROJECT_DIR$/bak/app/bak/common/lib/Pay/SDK/WeChatDeveloper/composer.json" />
    </option>
  </component>
  <component name="ComposerSettings" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="D:\ruanjian\phpstudy_pro\Extensions\php\php7.3.4nts\php.exe">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/myclabs/php-enum" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/qiniu/php-sdk" />
      <path value="$PROJECT_DIR$/vendor/deeplcom/deepl-php" />
      <path value="$PROJECT_DIR$/vendor/aliyuncs/oss-sdk-php" />
      <path value="$PROJECT_DIR$/vendor/maennchen/zipstream-php" />
      <path value="$PROJECT_DIR$/vendor/thans/tp-jwt-auth" />
      <path value="$PROJECT_DIR$/vendor/ezyang/htmlpurifier" />
      <path value="$PROJECT_DIR$/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/vendor/jianyan74/php-excel" />
      <path value="$PROJECT_DIR$/vendor/paypal/paypal-checkout-sdk" />
      <path value="$PROJECT_DIR$/vendor/paypal/rest-api-sdk-php" />
      <path value="$PROJECT_DIR$/vendor/php-http/multipart-stream-builder" />
      <path value="$PROJECT_DIR$/vendor/paypal/paypalhttp" />
      <path value="$PROJECT_DIR$/vendor/php-http/discovery" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php73" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-captcha" />
      <path value="$PROJECT_DIR$/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-view" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-client-contracts" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-helper" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/nyholm/psr7" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-client" />
      <path value="$PROJECT_DIR$/vendor/phpmailer/phpmailer" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-orm" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-multi-app" />
      <path value="$PROJECT_DIR$/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/vendor/qeq66/jwt" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-image" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-trace" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-template" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-filesystem" />
      <path value="$PROJECT_DIR$/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/vendor/topthink/framework" />
      <path value="$PROJECT_DIR$/bak/vendor/psr/log" />
      <path value="$PROJECT_DIR$/bak/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/bak/vendor/symfony/http-client-contracts" />
      <path value="$PROJECT_DIR$/bak/vendor/topthink/think-helper" />
      <path value="$PROJECT_DIR$/bak/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/bak/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/bak/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/bak/vendor/topthink/think-view" />
      <path value="$PROJECT_DIR$/bak/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/bak/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/bak/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/bak/vendor/nyholm/psr7" />
      <path value="$PROJECT_DIR$/bak/vendor/symfony/http-client" />
      <path value="$PROJECT_DIR$/bak/vendor/phpmailer/phpmailer" />
      <path value="$PROJECT_DIR$/bak/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/bak/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/bak/vendor/topthink/think-multi-app" />
      <path value="$PROJECT_DIR$/bak/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/bak/vendor/topthink/think-orm" />
      <path value="$PROJECT_DIR$/bak/vendor/topthink/think-trace" />
      <path value="$PROJECT_DIR$/bak/vendor/qeq66/jwt" />
      <path value="$PROJECT_DIR$/bak/vendor/topthink/think-image" />
      <path value="$PROJECT_DIR$/bak/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/bak/vendor/topthink/think-filesystem" />
      <path value="$PROJECT_DIR$/bak/vendor/topthink/think-template" />
      <path value="$PROJECT_DIR$/bak/vendor/psr/container" />
      <path value="$PROJECT_DIR$/bak/vendor/symfony/polyfill-php73" />
      <path value="$PROJECT_DIR$/bak/vendor/topthink/think-captcha" />
      <path value="$PROJECT_DIR$/bak/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/bak/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/bak/vendor/topthink/framework" />
      <path value="$PROJECT_DIR$/bak/vendor/myclabs/php-enum" />
      <path value="$PROJECT_DIR$/bak/vendor/composer" />
      <path value="$PROJECT_DIR$/bak/vendor/qiniu/php-sdk" />
      <path value="$PROJECT_DIR$/bak/vendor/deeplcom/deepl-php" />
      <path value="$PROJECT_DIR$/bak/vendor/aliyuncs/oss-sdk-php" />
      <path value="$PROJECT_DIR$/bak/vendor/maennchen/zipstream-php" />
      <path value="$PROJECT_DIR$/bak/vendor/thans/tp-jwt-auth" />
      <path value="$PROJECT_DIR$/bak/vendor/ezyang/htmlpurifier" />
      <path value="$PROJECT_DIR$/bak/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/bak/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/bak/vendor/paypal/paypal-checkout-sdk" />
      <path value="$PROJECT_DIR$/bak/vendor/jianyan74/php-excel" />
      <path value="$PROJECT_DIR$/bak/vendor/paypal/paypalhttp" />
      <path value="$PROJECT_DIR$/bak/vendor/php-http/discovery" />
      <path value="$PROJECT_DIR$/bak/vendor/paypal/rest-api-sdk-php" />
      <path value="$PROJECT_DIR$/bak/vendor/php-http/multipart-stream-builder" />
    </include_path>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2xuMJOCFTdkatmFJv6vSAJ0NnTR" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "D:/web/fb-mh/8.222.176.228_8000_br4mW/app/common/lib/Pay",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PS-241.18034.69" />
        <option value="bundled-php-predefined-ba97393d7c68-48a1a656d44e-com.jetbrains.php.sharedIndexes-PS-241.18034.69" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="8840d444-5d02-47f4-940d-4a10ec0c79a9" name="更改" comment="" />
      <created>1748783433667</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748783433667</updated>
      <workItem from="1748783434780" duration="576000" />
      <workItem from="1748784727906" duration="3340000" />
      <workItem from="1748846224105" duration="608000" />
      <workItem from="1748903193162" duration="4536000" />
      <workItem from="1748945291263" duration="6156000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>