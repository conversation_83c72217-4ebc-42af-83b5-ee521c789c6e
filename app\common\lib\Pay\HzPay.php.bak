<?php
// +----------------------------------------------------------------------
// | 星辰小牛Admin
// +----------------------------------------------------------------------
// | Website: ***.***.***
// +----------------------------------------------------------------------
// | Author: dav <***.***.***>
// +----------------------------------------------------------------------

namespace app\common\lib\Pay;

use app\common\model\Recharge as RechargeModel;
use think\Log;

/**
 * HzPay
 */
class HzPay {
    private $apiKey; // API密钥
    private $apiUrl; // API地址
    private $merchantId; // 商户ID

    public function __construct()
    {
        $this->apiKey = xn_cfg('general_website.hzpay_api_key');
        $this->apiUrl = xn_cfg('general_website.hzpay_api_url');
        $this->merchantId = xn_cfg('general_website.hzpay_merchant_id');
    }

    /**
     * 生成签名
     * @param array $params 请求参数（不含 sign）
     * @return string MD5 小写签名
     */
    protected function generateSign(array $params)
    {
        // 1. 过滤 null 值、空字符串和不参与签名的参数
        $filteredParams = [];
        foreach ($params as $key => $value) {
            if ($value !== null 
                && trim($value) !== '' 
                && !in_array($key, ['failReason', 'sign'])) {
                $filteredParams[$key] = $value;
            }
        }

        // 2. 按 ASCII 排序
        ksort($filteredParams);

        // 3. 拼接字符串 stringA
        $stringA = '';
        foreach ($filteredParams as $key => $value) {
            $stringA .= $key . '=' . $value . '&';
        }
        $stringA = rtrim($stringA, '&');

        // 4. 拼接密钥得到 stringB
        $stringB = $stringA . $this->apiKey;

        // 5. 计算 MD5
        return md5($stringB);
    }

    /**
     * 发起支付
     * @param array $order_data 订单数据
     * @return array
     * @throws \Exception
     */
    public function goPay($order_data)
    {
        $post_data = [
            'merchantId' => $this->merchantId,
            'eventType' => 'payin.order.create',
            'reference' => $order_data['order_no'],
            'customerName' => $order_data['username'] ?: 'Customer',
            'customerEmail' => $order_data['email'] ?: '<EMAIL>',
            'customerPhone' => $order_data['phone'] ?: '13800138000',
            'notifyUrl' => $order_data['notify_url'],
            'amount' => (string)$order_data['price'],
            'currency' => 'THB',
            'payMethod' => '14002'
        ];

        // 生成签名
        $post_data['sign'] = $this->generateSign($post_data);

        // 记录请求参数
        \think\facade\Log::info('HzPay请求参数', [
            'url' => $this->apiUrl,
            'data' => $post_data
        ]);

        // 发送请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            \think\facade\Log::error('HzPay请求错误: ' . curl_error($ch));
            curl_close($ch);
            throw new \Exception('支付请求网络错误');
        }
        
        curl_close($ch);

        if ($httpCode !== 200) {
            \think\facade\Log::error('HzPay请求失败，HTTP状态码: ' . $httpCode);
            throw new \Exception('支付请求失败，HTTP状态码: ' . $httpCode);
        }

        // 记录响应
        \think\facade\Log::info('HzPay响应', [
            'http_code' => $httpCode,
            'response' => $response
        ]);

        $result = json_decode($response, true);
        var_dump($result);
        if (!$result) {
            \think\facade\Log::error('HzPay响应解析失败: ' . $response);
            throw new \Exception('支付响应解析失败');
        }

        if ($result['statusCode'] !== 'success') {
            \think\facade\Log::error('HzPay业务处理失败: ' . ($result['statusMessage'] ?? '未知错误'));
            throw new \Exception('支付处理失败: ' . ($result['statusMessage'] ?? '未知错误'));
        }

        if (!isset($result['payUrl'])) {
            \think\facade\Log::error('HzPay响应缺少支付链接: ' . json_encode($result));
            throw new \Exception('支付响应异常：缺少支付链接');
        }

        return $result['payUrl'];
    }

    /**
     * 验证支付回调
     * @param array $data 回调数据
     * @return bool
     */
    public function notify($data)
    {
        if (!isset($data['sign'])) {
            return false;
        }

        $receivedSign = $data['sign'];
        $calculatedSign = $this->generateSign($data);

        return $receivedSign === $calculatedSign;
    }
} 