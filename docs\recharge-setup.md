# 充值功能安装配置指南

## 概述

本文档描述如何在现有项目中配置和使用HzPay充值功能。

## 1. 数据库配置

### 1.1 执行数据库迁移

运行以下SQL语句来添加充值功能所需的字段和表：

```bash
mysql -u your_username -p your_database < sql/recharge-enhancement.sql
```

或者手动执行SQL：

```sql
-- 为充值订单表添加第三方交易号字段
ALTER TABLE `xn_recharge` ADD COLUMN `pay_trade_no` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '第三方支付交易号' AFTER `pay_time`;

-- 添加索引
ALTER TABLE `xn_recharge` ADD INDEX `idx_type_uid` (`type`, `uid`);
ALTER TABLE `xn_recharge` ADD INDEX `idx_pay_trade_no` (`pay_trade_no`);
```

## 2. HzPay配置

### 2.1 获取HzPay商户信息

1. 联系HzPay获取商户账号
2. 获取以下信息：
   - 商户ID (Merchant ID)
   - API密钥 (API Key)
   - API地址 (API URL)

### 2.2 配置环境变量

在项目根目录的`.env`文件中添加以下配置：

```env
# 充值配置
RECHARGE_MIN_AMOUNT=1
RECHARGE_MAX_AMOUNT=10000
RECHARGE_ENABLED=true

# HzPay支付配置
HZPAY_ENABLED=true
HZPAY_API_KEY=your_actual_api_key_here
HZPAY_API_URL=https://api.hzpay.com/gateway
HZPAY_MERCHANT_ID=your_actual_merchant_id_here
```

### 2.3 配置说明

- `RECHARGE_MIN_AMOUNT`: 最小充值金额
- `RECHARGE_MAX_AMOUNT`: 最大充值金额
- `RECHARGE_ENABLED`: 是否启用充值功能
- `HZPAY_ENABLED`: 是否启用HzPay支付
- `HZPAY_API_KEY`: HzPay API密钥
- `HZPAY_API_URL`: HzPay API地址
- `HZPAY_MERCHANT_ID`: HzPay商户ID

## 3. 系统配置

### 3.1 后台配置项

在系统后台添加以下配置项（如果使用后台配置管理）：

```php
// 在 general_website 配置组中添加
'hzpay_api_key' => 'your_api_key',
'hzpay_api_url' => 'https://api.hzpay.com/gateway',
'hzpay_merchant_id' => 'your_merchant_id',
'hzpay_status' => 1, // 1=启用，0=禁用
'min_recharge_amount' => 1,
'max_recharge_amount' => 10000,
```

## 4. 路由配置

### 4.1 API路由

确保以下路由可以正常访问：

```
POST /api/recharge/create_order      - 创建充值订单
POST /api/recharge/query_order       - 查询订单状态
GET  /api/recharge/get_recharge_list - 获取充值记录
GET  /api/recharge/get_recharge_config - 获取充值配置
POST /api/recharge/notify_hzpay      - HzPay支付回调（无需认证）
```

### 4.2 回调地址配置

在HzPay商户后台配置回调地址：

```
https://your-domain.com/api/recharge/notify_hzpay
```

## 5. 前端集成

### 5.1 充值页面示例

```html
<!DOCTYPE html>
<html>
<head>
    <title>账户充值</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>
    <div id="recharge-app">
        <h2>账户充值</h2>
        
        <!-- 充值金额选择 -->
        <div class="amount-section">
            <h3>选择充值金额</h3>
            <div class="amount-buttons">
                <button v-for="amount in config.recharge_amounts" 
                        :key="amount"
                        @click="selectAmount(amount)"
                        :class="{'active': selectedAmount === amount}">
                    ¥{{amount}}
                </button>
            </div>
            
            <!-- 自定义金额 -->
            <div class="custom-amount">
                <label>自定义金额：</label>
                <input type="number" 
                       v-model="customAmount" 
                       :min="config.min_amount" 
                       :max="config.max_amount"
                       placeholder="请输入充值金额">
            </div>
        </div>
        
        <!-- 支付方式 -->
        <div class="payment-section">
            <h3>支付方式</h3>
            <div class="payment-method">
                <label>
                    <input type="radio" value="13" v-model="payMethod">
                    HzPay支付
                </label>
            </div>
        </div>
        
        <!-- 充值按钮 -->
        <div class="submit-section">
            <button @click="createOrder" :disabled="loading || !canSubmit">
                {{loading ? '处理中...' : '立即充值'}}
            </button>
        </div>
        
        <!-- 充值记录 -->
        <div class="history-section">
            <h3>充值记录</h3>
            <div v-if="rechargeList.length === 0" class="no-data">
                暂无充值记录
            </div>
            <div v-else>
                <div v-for="item in rechargeList" :key="item.id" class="record-item">
                    <div class="record-info">
                        <span class="amount">¥{{item.amount}}</span>
                        <span class="status" :class="'status-' + item.pay_status">
                            {{item.pay_status_text}}
                        </span>
                    </div>
                    <div class="record-detail">
                        <span>订单号：{{item.order_no}}</span>
                        <span>时间：{{formatTime(item.create_time)}}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    config: {},
                    selectedAmount: 0,
                    customAmount: '',
                    payMethod: 13,
                    loading: false,
                    rechargeList: []
                }
            },
            
            computed: {
                finalAmount() {
                    return this.customAmount || this.selectedAmount;
                },
                
                canSubmit() {
                    return this.finalAmount > 0 && 
                           this.finalAmount >= this.config.min_amount && 
                           this.finalAmount <= this.config.max_amount;
                }
            },
            
            async mounted() {
                await this.getConfig();
                await this.getRechargeList();
            },
            
            methods: {
                // 获取配置
                async getConfig() {
                    try {
                        const response = await fetch('/api/recharge/get_recharge_config', {
                            headers: {
                                'Authorization': 'Bearer ' + localStorage.getItem('token')
                            }
                        });
                        const result = await response.json();
                        if (result.code === 1) {
                            this.config = result.data;
                        }
                    } catch (error) {
                        console.error('获取配置失败:', error);
                    }
                },
                
                // 选择金额
                selectAmount(amount) {
                    this.selectedAmount = amount;
                    this.customAmount = '';
                },
                
                // 创建订单
                async createOrder() {
                    if (!this.canSubmit) return;
                    
                    this.loading = true;
                    
                    try {
                        const response = await fetch('/api/recharge/create_order', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': 'Bearer ' + localStorage.getItem('token')
                            },
                            body: JSON.stringify({
                                amount: this.finalAmount,
                                pay_method: this.payMethod,
                                client: 2
                            })
                        });
                        
                        const result = await response.json();
                        
                        if (result.code === 1) {
                            alert('订单创建成功，正在跳转支付...');
                            window.location.href = result.data.pay_url;
                        } else {
                            alert(result.msg);
                        }
                    } catch (error) {
                        console.error('创建订单失败:', error);
                        alert('网络错误，请重试');
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 获取充值记录
                async getRechargeList() {
                    try {
                        const response = await fetch('/api/recharge/get_recharge_list?limit=5', {
                            headers: {
                                'Authorization': 'Bearer ' + localStorage.getItem('token')
                            }
                        });
                        const result = await response.json();
                        if (result.code === 1) {
                            this.rechargeList = result.data.list;
                        }
                    } catch (error) {
                        console.error('获取记录失败:', error);
                    }
                },
                
                // 格式化时间
                formatTime(timestamp) {
                    return new Date(timestamp * 1000).toLocaleString();
                }
            }
        }).mount('#recharge-app');
    </script>
    
    <style>
        .amount-buttons button {
            margin: 5px;
            padding: 10px 20px;
            border: 1px solid #ddd;
            background: #fff;
            cursor: pointer;
        }
        
        .amount-buttons button.active {
            background: #007bff;
            color: white;
        }
        
        .record-item {
            border: 1px solid #eee;
            padding: 10px;
            margin: 5px 0;
        }
        
        .status-1 { color: green; }
        .status-0 { color: orange; }
        .status-2 { color: red; }
    </style>
</body>
</html>
```

## 6. 测试验证

### 6.1 功能测试

1. **配置测试**:
   ```bash
   curl -X GET "https://your-domain.com/api/recharge/get_recharge_config" \
        -H "Authorization: Bearer your_token"
   ```

2. **创建订单测试**:
   ```bash
   curl -X POST "https://your-domain.com/api/recharge/create_order" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer your_token" \
        -d '{"amount": 10, "pay_method": 13, "client": 2}'
   ```

### 6.2 支付流程测试

1. 创建测试订单
2. 访问返回的支付链接
3. 完成支付流程
4. 验证回调处理
5. 检查用户余额是否增加

## 7. 安全注意事项

1. **HTTPS**: 生产环境必须使用HTTPS
2. **API密钥**: 妥善保管HzPay API密钥
3. **回调验证**: 严格验证支付回调签名
4. **金额验证**: 前后端都要验证充值金额
5. **日志记录**: 记录所有支付相关操作

## 8. 故障排除

### 8.1 常见问题

1. **"支付方式配置错误"**:
   - 检查HzPay类文件是否存在
   - 确认支付方式ID配置正确

2. **"支付请求失败"**:
   - 检查HzPay API配置是否正确
   - 确认网络连接正常

3. **"回调验证失败"**:
   - 检查API密钥是否正确
   - 确认签名算法实现正确

### 8.2 调试方法

1. 查看应用日志：
   ```bash
   tail -f runtime/log/202X/XX/XX.log
   ```

2. 启用调试模式：
   ```env
   APP_DEBUG=true
   ```

## 9. 更新日志

- **v1.0.0** (2024-01-01): 初始版本，支持HzPay充值功能
