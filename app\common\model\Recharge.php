<?php
// +----------------------------------------------------------------------
// | 星辰小牛Admin
// +----------------------------------------------------------------------
// | Website: ***.***.***
// +----------------------------------------------------------------------
// | Author: dav <***.***.***>
// +----------------------------------------------------------------------

namespace app\common\model;
class Recharge extends SetModel
{
    protected $autoWriteTimestamp = true;
    // 设置json类型字段
	protected $json = ['post_data'];
	// 设置JSON数据返回数组
    protected $jsonAssoc = true;
    // 追加字段
    protected $append = [
        'pay_status_text',
        'pay_method_text',
        'client_text',
        'type_text',
    ];
    
    /**
     * 获取器 - 获取订单类型
     */
    public function getTypeTextAttr($value,$data){
        $type = $this->get_type_list();
        return $type[$data['type']];
    }
    
    public function get_type_list(){
        return [1=>'购买商品',2=>'开盲盒',3=>'支付运费',4=>'账户充值'];
    }
    //订单类型方法名称
    public function get_type_fun(){
        return [
            1=>'add_goods_order',
            2=>'add_blind_box_order',
            3=>'batch_apply_deliver',
        ];
    }
    
    /**
     * 获取器 - 获取订单状态
     */
    public function getPayStatusTextAttr($value,$data){
        $pay_status = $this->get_pay_status_list();
        return $pay_status[$data['pay_status']];
    }
    
    public function get_pay_status_list(){
        return [0=>'等待支付',1=>'完成支付',2=>'已退款'];
    }
    
    /**
     * 获取器 - 获取支付方式
     */
    public function getPayMethodTextAttr($value,$data){
        $pay_method = $this->get_pay_method_list();
        return $pay_method[$data['pay_method']];
    }
    
    public function get_pay_method_list(){
        return [1=>'小程序微信支付',2=>'APP支付宝支付',3=>'APP微信支付',4=>'H5易支付微信',5=>'H5易支付支付宝',6=>'幸运币支付',7=>'H5官方微信',8=>'H5官方支付宝',9=>'paypal支付',10=>'ottpay支付',11=>'巴西支付',12=>'HwdzPay支付',13=>'HzPay支付'];
    }
    public function get_pay_method_notify_class(){
        return [
            1=>'WeChatPay', //小程序微信支付
            2=>'AppAliPay', //APP支付宝支付
            3=>'AppWeChatPay', //APP微信支付
            4=>'Epay', //易支付微信
            5=>'Epay', //易支付支付宝
            6=>'IntegralPay', //积分支付
            7=>'H5WeChatPay', //H5微信支付
            8=>'H5Alipay', //H5支付宝支付
            9=>'Paypal', //Paypal支付
            10=>'OttPay', //OttPay支付
            11=>'BaxiPay', //BaxiPay支付
            12=>'HwdzPay', //HwdzPay支付
            13=>'HzPay', //HzPay支付
        ];
    }
    
    /**
     * 获取器 - 获取客户端类型
     */
    public function getClientTextAttr($value,$data){
        $client = $this->get_client_list();
        return $client[$data['client']];
    }
    
    public function get_client_list(){
        return [0=>'web',1=>'微信小程序',2=>'app'];
    }
    
    /**
     * 获取盲盒支付价格
     * $blind_box 盲盒数据
     * $member 会员数据
     * $buy_fangan 购买方案数据
     * $currentCoupon_id 优惠券ID
    **/
    public function get_box_price($blind_box,$member,$buy_fangan,$post=[]){
        if($post['paymode']=='integral'){ //幸运币支付
            $pay_price_field = 'integral_price';
        }else{
            $pay_price_field = 'price'; 
        }
        $offer_remarks = '';
        $pay_price = $blind_box[$pay_price_field] * $buy_fangan['number'];
        if($blind_box['is_new_user_discount']==1 && $buy_fangan['number']>1){ //新用户首单优惠的，并且开盒次数大于1
            $pay_price = $blind_box[$pay_price_field] * $buy_fangan['number'];
            $BlindBoxOrder = new BlindBoxOrder;
            $my_box_order = $BlindBoxOrder->where('uid',$member['id'])->where('blind_box_id',$blind_box['id'])->count(); //判断该用户是否购买过该盲盒
            // $my_box_order = 0;
            if($my_box_order==0){ //未购买过，重新定价
                $offer_remarks .= fanyi('新用户首次购买，首件0.01元。');
                $pay_price = ($blind_box[$pay_price_field] * ($buy_fangan['number'] - 1)) + 0.01;
            }
        }
        
        if($buy_fangan['discount']>0 && $blind_box['is_new_user_discount']!=1 || $my_box_order>0){ //优惠折扣大于0并且不是新人优惠盒子
            $offer_remarks .= fanyi('购买方案'.$buy_fangan['title'].'优惠了：'.round($pay_price * $buy_fangan['discount'],3).'元。');
            $pay_price = round($pay_price - ($pay_price * $buy_fangan['discount']),3); //通过购买方案折扣重新定义支付价格
        }
        
        if(empty($post['currentCoupon_id'])){
            $currentCoupon_id = 0;
        }else{
            $currentCoupon_id = $post['currentCoupon_id'];
        }
        
        if($currentCoupon_id!=0){
            $MemberCouponList = new MemberCouponList;
            $my_coupon = $MemberCouponList->get_use_coupon_price($currentCoupon_id,$member);
            if($my_coupon!==false){
                $pay_price = $pay_price - $my_coupon['coupon_data']['number']; //优惠券效验通过，重新定义支付价格
                $offer_remarks .= fanyi('使用优惠券'.$my_coupon['coupon_data']['title'].'抵扣了'.$my_coupon['coupon_data']['number'].'元。');
            }
        }
        
        $xingshi = 0;
        //用户有星石，使用星石抵扣金额
        if($member['integral2']>0){
            $integral2_dikou_max = round($blind_box['price'] * $buy_fangan['number'] * 0.1,3); //最大抵扣购买金额的十分之一
            $integral2_dikou = round($member['integral2'] / $blind_box['integral2_dikou_rmb'],3); //定义星石抵扣金额
            if($integral2_dikou>$integral2_dikou_max){ //如果用户的可抵扣星石大于最大抵扣金额
                $xingshi_dikou = $integral2_dikou_max;
            }else{
                $xingshi_dikou = $integral2_dikou; //使用星石抵扣，重新定义支付价格
            }
            // $xingshi = ceil($xingshi_dikou * $blind_box['integral2_dikou_rmb']); //计算消耗了多少星石(向上取整)
            $xingshi = round($xingshi_dikou * $blind_box['integral2_dikou_rmb'],3); //计算消耗了多少星石
            // var_dump($xingshi);die;
            if($xingshi>0){
                $pay_price = $pay_price - $xingshi_dikou;
                $offer_remarks .= fanyi('使用'.$xingshi.'星石抵扣了'.$xingshi_dikou.'元。');
            }
        }
        if($pay_price<=0){
            $pay_price = 0;
        }else if($post['paymode']!='integral' && $pay_price>0){
            $rand_pay_number = xn_cfg('general_canshu.rand_pay_number');
            if(isset($rand_pay_number) && $rand_pay_number!=0){
                $rand_pay_number = explode('-',$rand_pay_number);
                $randomNumber = mt_rand($rand_pay_number[0],$rand_pay_number[1]);
                // var_dump($randomNumber);die;
                if(is_numeric($randomNumber)){
                    $pay_price += round($randomNumber / 100,2);
                }
            }
        }
        return [
            'pay_price'=>round($pay_price,3),
            'offer_remarks'=>$offer_remarks,
            'xingshi'=>$xingshi,
        ];
    }
    
}