<?php
// +----------------------------------------------------------------------
// | 星辰小牛Admin
// +----------------------------------------------------------------------
// | Website: ***.***.***
// +----------------------------------------------------------------------
// | Author: dav <***.***.***>
// +----------------------------------------------------------------------

namespace app\api\controller;
use app\common\controller\ApiBase;
use app\api\middleware\Jwt;
use app\common\model\GoodsOrder as GoodsOrderModel;
use app\common\model\BlindBoxOrder as BlindBoxOrderModel;
use app\common\model\BlindBoxPrizeRecord as BlindBoxPrizeRecordModel;
use app\common\model\BlindBoxPrizeGiveLog as BlindBoxPrizeGiveLogModel;
use app\common\model\Member as MemberModel;
use app\common\model\MemberAddress as MemberAddressModel;
use app\common\model\Kuaidi100Code as Kuaidi100CodeModel;
use app\common\model\MemberCard as MemberCardModel;
use app\common\model\Card as CardModel;
use app\common\model\SysReward as SysRewardModel;
use app\common\model\Goods as GoodsModel;
use app\common\model\GoodsOrderKami as GoodsOrderKamiModel;
use app\common\model\MemberMoneyLog as MemberMoneyLogModel;
use app\common\lib\Kuaidi100;
use think\facade\Db;
class Order extends ApiBase
{
    /**
     * 控制器中间件 [登录、注册 不需要鉴权]
     * @var array
     */
	protected $middleware = [
    	Jwt::class => ['except' 	=> [] ]
    ];
    
    
    //获取我的商品订单记录
    public function get_goods_order(){
        $GoodsOrderModel = new GoodsOrderModel;
        $status = input('status',0,'int');
        if($status!=0){
            $GoodsOrderModel = $GoodsOrderModel->where('status',$status);
        }
        $list = $GoodsOrderModel->where('uid',$this->get_uid())->order('id desc')->page(input('page'),10)->select();
        foreach ($list as &$item){
            $item['order_price'] = $item['number'] * $item['price'];
            $item['status_text2'] = fanyi($item['status_text']);
            $item['source_text2'] = fanyi($item['source_text']);
        }
        $this->success('查询成功',$list);
    }
    
    //物流信息查询
    public function get_express_data(){
        $express_company = input('express_company','');
        $express_number = input('express_number','');
        if($express_company==''||$express_number==''){
            $this->error('快递物流信息查询参数异常');
        }
        $Kuaidi100CodeModel = new Kuaidi100CodeModel;
        $kuaidi100_code = $Kuaidi100CodeModel->get_find([ ['title','=',$express_company] ]); //通过快递名称查询编码
        if(!$kuaidi100_code){
            $this->error('快递物流信息查询参数异常');
        }
        $express_company = $kuaidi100_code['code'];
        
        $Kuaidi100 = new Kuaidi100;
        $data = $Kuaidi100->get_data($express_company,$express_number);
        $this->success('',$data);
    }
    
    //获取我的商品订单详情
    public function get_goods_order_info(){
        $GoodsOrderModel = new GoodsOrderModel;
        $info = $GoodsOrderModel->where('id',input('order_id'))->find();
        if(!$info || $info['uid']!=$this->get_uid()){
            $this->error('订单查询不存在');
        }
        $info['order_price'] = $info['number'] * $info['price'];
        if($info['goods_type']==2){ //卡密商品，返回卡密数据
            $GoodsOrderKamiModel = new GoodsOrderKamiModel;
            $order_kami_list = $GoodsOrderKamiModel->where('order_id',$info['id'])->select();
            foreach ($order_kami_list as &$item){
                $kami_data = $item['kami_data'];
                if($kami_data['end_time']>1000){
                    $kami_data['end_time'] = date('Y-m-d H:i:s',$kami_data['end_time']);
                    $item['kami_data'] = $kami_data;
                }
            }
            $info['kami_data'] = $order_kami_list;
        }
        $info['status_text2'] = fanyi($info['status_text']);
        $info['source_text2'] = fanyi($info['source_text']);
        $info['express_data'] = json_decode($info['express_data'],true);
        $this->success('查询成功',$info);
    }
    
    //获取我的奖品记录
    public function get_prize_record(){
        $BlindBoxPrizeRecordModel = new BlindBoxPrizeRecordModel;
        $where = [];
        $where[] = ['status','=',input('status',0,'int')];
        $list = $BlindBoxPrizeRecordModel->order('id desc')->where('uid',$this->get_uid())->where($where)->page(input('page',1),10)->select();
        foreach ($list as &$item){
            $item['exchange_time'] = date('Y-m-d H:i:s',$item['exchange_time']);
            $item['exchange_integral'] = $item['exchange_integral_text'];
        }
        
        $SysRewardModel = new SysRewardModel;
        $sys_reward = $SysRewardModel->get_list([ ['type','=','4'],['status','=',1] ]);
        if($sys_reward){ //判断是否存在首次兑换活动
            $cck = BlindBoxPrizeRecordModel::where('uid',$this->get_uid())->where('status',2)->count(); //查询已回收产品数量
        }else{
            $cck = 1;
        }
        
        $freight_set = xn_cfg('general_freight_set');
        $freight_logic = $freight_set['freight_logic'];
        if($freight_logic==1){
            $freight_rule_text = fanyi('一次性提货满'.$freight_set['avoid_freight'].'件后免运费(自动发货商品不算)');
        }else if($freight_logic==2){
            $freight_rule_text = fanyi('每满'.$freight_set['number_one_freight'].'件只收一件运费(自动发货商品不算)');
        }else{
            $freight_rule_text = '';
        }
        
        $data = [
            'list'=>$list,
            'cck'=> $cck,
            'freight_rule_text'=>$freight_rule_text,
        ];
        $this->success('查询成功',$data);
    }
    
    //获取未开盒的盲盒
    public function get_blind_box_order(){
        $BlindBoxOrderModel = new BlindBoxOrderModel;
        $list = $BlindBoxOrderModel->order('id desc')
        ->where('uid',$this->get_uid())->where('status',0)->page(input('page',1),10)->select();
        $this->success('查询成功',$list);
    }
    
    //获取商城订单各状态的数量
    public function get_order_status_count(){
        $GoodsOrderModel = new GoodsOrderModel;
        $dfh = $GoodsOrderModel->where('uid',$this->get_uid())->where('status',1)->count();
        $dsh = $GoodsOrderModel->where('uid',$this->get_uid())->where('status',2)->count();
        $data = [
            'dfh'=>$dfh,
            'dsh'=>$dsh,
        ];
        $this->success('',$data);
    }
    
    //奖品兑换回收
    public function prize_exchange(){
        $ids = input('id','');
        $BlindBoxPrizeRecordModel = new BlindBoxPrizeRecordModel;
        $BlindBoxPrizeRecordModel = $BlindBoxPrizeRecordModel->where('uid',$this->get_uid());
        $list = $BlindBoxPrizeRecordModel->where('id','in',$ids)->lock(true)->where('status',0)->select();
        if(!$list){
            $this->error('未选中兑换产品');
        }
        $ok_num = 0;
        $exchange_num = BlindBoxPrizeRecordModel::where('uid',$this->get_uid())->where('status',2)->count(); //查询用户已回收次数
        foreach($list as $k => $v){
            if($v['exchange_integral_text']<=0){ //回收积分大于0才能回收
                continue;
            }
            if($ok_num==0 && $exchange_num<=0){ //用户已回收次数小于0,并且本次第一次回收成功就执行首次兑换活动奖励
                $SysRewardModel = new SysRewardModel;
                $SysRewardModel->reward_logic($this->get_uid(),4,$v['id'],'首次兑换赠送');
            }
            $BlindBoxPrizeRecordModel = new BlindBoxPrizeRecordModel;
            $res = $BlindBoxPrizeRecordModel->prize_exchange($v['id']);
            if($res===true){
                $ok_num += 1;
            }
        }
        $this->success('成功回收'.$ok_num.'个商品');
    }
    
    //奖品转增给他人
    public function prize_give(){
        $ids = input('id','');
        $receive_username = input('receive_username',0);
        $BlindBoxPrizeRecordModel = new BlindBoxPrizeRecordModel;
        $BlindBoxPrizeRecordModel = $BlindBoxPrizeRecordModel->where('uid',$this->get_uid());
        $list = $BlindBoxPrizeRecordModel->where('id','in',$ids)->lock(true)->where('status',0)->select();
        if(!$list){
            $this->error('未选中转增产品');
        }
        $MemberModel = new MemberModel;
        $receive_member = $MemberModel->where('username|id',$receive_username)->find();
        if(!$receive_member || $receive_member['id']==$this->get_uid()){
            $this->error('接收人不存在');
        }
        
        $ok_num = 0;
        foreach($list as $k => $v){
            $BlindBoxPrizeRecordModel = new BlindBoxPrizeRecordModel;
            $res = $BlindBoxPrizeRecordModel->prize_give($v['id'],$receive_member);
            if($res===true){
                $ok_num += 1;
            }
        }
        $this->success('成功转增'.$ok_num.'个商品');
    }
    
    //查询我转增给他人的记录
    public function prize_give_log(){
        $BlindBoxPrizeGiveLogModel = new BlindBoxPrizeGiveLogModel;
        $list = $BlindBoxPrizeGiveLogModel
        ->where('give_uid',$this->get_uid())->whereOr('receive_uid',$this->get_uid())
        ->order('id desc')->page(input('page',1),10)->select();
        foreach ($list as &$item) {
            if($item['give_uid']==$this->get_uid()){ //如果自己是赠送人
                $item['status_text'] = fanyi('赠送他人');
            }else{
                $item['status'] = fanyi('他人赠送');
            }
        }
        $this->success('查询成功',$list);
    }
    
    //奖品重抽
    public function redraw_prize(){
        $card_id = input('card_id',0,'int');
        $prize_id = input('id',0,'int');
        // 启动事务
        Db::startTrans();
        try {
            $MemberCardModel = new MemberCardModel;
            $card = $MemberCardModel->lock(true)->find($card_id);
            if(!$card || $card['uid']!=$this->get_uid() || $card['card_data']['type']!=1 || $card['status']!=1){
                $this->error('重抽卡不存在');
            }
            $BlindBoxPrizeRecordModel = new BlindBoxPrizeRecordModel;
            $prize = $BlindBoxPrizeRecordModel->lock(true)->find($prize_id);
            if(!$prize || $prize['uid']!=$this->get_uid() || $prize['status']!=0){
                $this->error('奖品不存在');
            }
            $prize->status = 3;
            $prize->save();
            $card->status = 2;
            $card->save();
            $BlindBoxOrderModel = new BlindBoxOrderModel;
            $res = $BlindBoxOrderModel->add_order(
                get_order('mhcz-'),
                $prize['blind_box_id'],
                $prize['uid'],
                3,
                1,
                $prize['id']
            );
            // 提交事务
            Db::commit();
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('ok');
    }
    
    //申请发货获取是否有需支付的费用
    public function get_apply_deliver_price(){
        $ids = input('ids',0);
        $BlindBoxPrizeRecordModel = new BlindBoxPrizeRecordModel;
        if(input('ids')=='new_prize'){
            $list = $BlindBoxPrizeRecordModel->where('status',0)->where('uid',$this->get_uid())->order('id desc')->limit(input('num',10))->select();
        }else{
            $list = $BlindBoxPrizeRecordModel->where('id','in',$ids)->where('status',0)->select();
        }
        if(!$list){
            $this->success('ok',0);
        }
        $freight_value = $BlindBoxPrizeRecordModel->get_apply_deliver_price($list);
        $MemberModel = new MemberModel;
        $member = $MemberModel->get_info($this->get_uid());
        if($member['yunfei_balance']>=$freight_value){
            $yi_jian_mian = $freight_value;
            $freight_value = 0;
        }else if($member['yunfei_balance']>0){
            $yi_jian_mian = $member['yunfei_balance'];
            $freight_value = $freight_value - $member['yunfei_balance'];
        }
        
      //  $freight_value=100;
        
        $this->success('ok',['freight_value'=>$freight_value,'yi_jian_mian'=>$yi_jian_mian]);
    }
    
    //申请发货
    public function apply_deliver(){
        $ids = input('ids',0);
        $address_id = input('address_id',0);
        $remarks = input('remarks','');
        $MemberAddressModel = new MemberAddressModel;
        $my_address = $MemberAddressModel->where('id',$address_id)->find();
        if(!$my_address || $my_address['uid']!=$this->get_uid()){
            $this->error('收货地址不存在');
        }
        $BlindBoxPrizeRecordModel = new BlindBoxPrizeRecordModel;
        $BlindBoxPrizeRecordModel = $BlindBoxPrizeRecordModel->where('uid',$this->get_uid());
        if(input('ids')=='new_prize'){
            $list = $BlindBoxPrizeRecordModel->where('status',0)->order('id desc')->limit(input('num',10))->select();
        }else{
            $list = $BlindBoxPrizeRecordModel->where('id','in',$ids)->where('status',0)->select();
        }
        
        if(!$list){
            $this->error('未选中提货产品');
        }
        foreach ($list as $k => $v){
            $GoodsModel = new GoodsModel;
            $goods_info = $GoodsModel->find($v['goods_id']);
            if($goods_info && $goods_info['stock']<=0){
                $this->error('您选中的提货商品：'.$goods_info['title'].'，库存不足！');
            }
        }
        $freight_value = BlindBoxPrizeRecordModel::get_apply_deliver_price($list);
        
        $MemberModel = new MemberModel;
        $member = $MemberModel->get_info($this->get_uid());
        if($member['yunfei_balance']>=$freight_value){
            $yi_jian_mian = $freight_value;
            $freight_value = 0;
        }else if($member['yunfei_balance']>0){
            $yi_jian_mian = $member['yunfei_balance'];
            $freight_value = $freight_value - $member['yunfei_balance'];
        }
        
        if($freight_value>0){ //运费大于0 去支付运费
            $Pay = new \app\api\controller\Pay($this->app);
            return $Pay->pay_freight_value(input(),$ids,$freight_value,$this->get_uid());
        }else{
            
            $MemberMoneyLogModel = new MemberMoneyLogModel;
            $MemberMoneyLogModel->set_money($member['pid'],$yi_jian_mian,2,5,'抵扣运费扣除');
            
            $ok_num = 0;
            foreach($list as $k => $v){
                $BlindBoxPrizeRecordModel = new BlindBoxPrizeRecordModel;
                $res = $BlindBoxPrizeRecordModel->apply_deliver($v['id'],$my_address,$remarks);
                if($res===true){
                    $ok_num += 1;
                }
            }
            $this->success('成功提货'.$ok_num.'个商品','','',8);
        }
    }
    
    //确认收货
    public function confirm_deliver(){
        $id = input('id',0,'int');
        $GoodsOrderModel = new GoodsOrderModel;
        $info = $GoodsOrderModel->find($id);
        if( !$info || $info['uid']!=$this->get_uid() ){
            $this->error('订单不存在');
        }
        if($info['status']!=2){ //不是发货状态
            $this->error('订单状态不为待收货');
        }
        $info->status = 3;
        $info->save();
        $this->success('订单交易完成');
    }
    
}