# 充值接口API文档

## 概述

本文档描述了账户充值功能的API接口，支持HzPay支付方式。

**技术特点**：
- 支持HzPay第三方支付
- 完整的订单状态管理
- 安全的支付回调验证
- 详细的充值记录查询

## 基础信息

- **基础URL**: `https://your-domain.com/api/recharge`
- **认证方式**: JWT Token (Bearer Token)
- **数据格式**: JSON

## 接口列表

### 1. 创建充值订单

创建充值订单并获取支付链接。

#### 请求信息

- **URL**: `/create_order`
- **方法**: `POST`
- **认证**: 需要JWT Token

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| amount | float | 是 | 充值金额，必须大于0 |
| pay_method | int | 否 | 支付方式，默认13（HzPay） |
| client | int | 否 | 客户端类型：0=web, 1=微信小程序, 2=app，默认2 |

#### 请求示例

```json
{
    "amount": 100.00,
    "pay_method": 13,
    "client": 2
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
    "code": 1,
    "msg": "订单创建成功",
    "data": {
        "order_no": "CZ-20241201123456789",
        "order_id": 12345,
        "amount": 100.00,
        "pay_url": "https://pay.hzpay.com/gateway?token=xxx",
        "pay_method": 13,
        "pay_method_name": "HzPay支付"
    }
}
```

**失败响应 (400)**:
```json
{
    "code": 0,
    "msg": "充值金额必须大于0"
}
```

```json
{
    "code": 0,
    "msg": "充值金额不能少于1元"
}
```

### 2. 查询充值订单状态

查询指定订单的支付状态。

#### 请求信息

- **URL**: `/query_order`
- **方法**: `POST`
- **认证**: 需要JWT Token

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| order_no | string | 是 | 订单号 |

#### 请求示例

```json
{
    "order_no": "CZ-20241201123456789"
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
    "code": 1,
    "msg": "查询成功",
    "data": {
        "order_no": "CZ-20241201123456789",
        "amount": 100.00,
        "pay_status": 1,
        "pay_status_text": "完成支付",
        "pay_method": 13,
        "pay_method_text": "HzPay支付",
        "create_time": 1701234567,
        "pay_time": 1701234600
    }
}
```

### 3. 获取充值记录

获取用户的充值历史记录。

#### 请求信息

- **URL**: `/get_recharge_list`
- **方法**: `GET`
- **认证**: 需要JWT Token

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认10 |

#### 请求示例

```
GET /api/recharge/get_recharge_list?page=1&limit=10
```

#### 响应示例

**成功响应 (200)**:
```json
{
    "code": 1,
    "msg": "查询成功",
    "data": {
        "list": [
            {
                "id": 12345,
                "order_no": "CZ-20241201123456789",
                "amount": 100.00,
                "pay_status": 1,
                "pay_status_text": "完成支付",
                "pay_method": 13,
                "pay_method_text": "HzPay支付",
                "create_time": 1701234567,
                "pay_time": 1701234600
            }
        ],
        "total": 25,
        "page": 1,
        "limit": 10
    }
}
```

### 4. 获取充值配置

获取充值相关的配置信息。

#### 请求信息

- **URL**: `/get_recharge_config`
- **方法**: `GET`
- **认证**: 需要JWT Token

#### 请求参数

无需参数

#### 响应示例

**成功响应 (200)**:
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "min_amount": 1.00,
        "max_amount": 10000.00,
        "hzpay_status": true,
        "recharge_amounts": [10, 50, 100, 200, 500, 1000]
    }
}
```

## 支付状态说明

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 等待支付 | 订单已创建，等待用户支付 |
| 1 | 完成支付 | 支付成功，余额已到账 |
| 2 | 已退款 | 订单已退款 |

## 支付方式说明

| 方式ID | 支付方式 | 说明 |
|--------|----------|------|
| 13 | HzPay支付 | 第三方HzPay支付网关 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 请求失败 |
| 1 | 请求成功 |

## 前端集成示例

### JavaScript示例

```javascript
// 1. 获取充值配置
async function getRechargeConfig() {
    try {
        const response = await fetch('/api/recharge/get_recharge_config', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + localStorage.getItem('token')
            }
        });
        const result = await response.json();
        
        if (result.code === 1) {
            console.log('充值配置:', result.data);
            return result.data;
        } else {
            console.error('获取配置失败:', result.msg);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 2. 创建充值订单
async function createRechargeOrder(amount) {
    try {
        const response = await fetch('/api/recharge/create_order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + localStorage.getItem('token')
            },
            body: JSON.stringify({
                amount: amount,
                pay_method: 13, // HzPay
                client: 2 // APP
            })
        });
        
        const result = await response.json();
        
        if (result.code === 1) {
            console.log('订单创建成功:', result.data);
            // 跳转到支付页面
            window.location.href = result.data.pay_url;
            return result.data;
        } else {
            console.error('订单创建失败:', result.msg);
            alert(result.msg);
        }
    } catch (error) {
        console.error('请求失败:', error);
        alert('网络错误，请重试');
    }
}

// 3. 查询订单状态
async function queryOrderStatus(orderNo) {
    try {
        const response = await fetch('/api/recharge/query_order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + localStorage.getItem('token')
            },
            body: JSON.stringify({
                order_no: orderNo
            })
        });
        
        const result = await response.json();
        
        if (result.code === 1) {
            console.log('订单状态:', result.data);
            return result.data;
        } else {
            console.error('查询失败:', result.msg);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 4. 获取充值记录
async function getRechargeList(page = 1, limit = 10) {
    try {
        const response = await fetch(`/api/recharge/get_recharge_list?page=${page}&limit=${limit}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + localStorage.getItem('token')
            }
        });
        
        const result = await response.json();
        
        if (result.code === 1) {
            console.log('充值记录:', result.data);
            return result.data;
        } else {
            console.error('获取记录失败:', result.msg);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}
```

### Vue.js示例

```javascript
// 充值组件
export default {
    data() {
        return {
            amount: 0,
            config: {},
            loading: false
        }
    },
    
    async mounted() {
        await this.getConfig();
    },
    
    methods: {
        // 获取配置
        async getConfig() {
            try {
                const response = await this.$http.get('/api/recharge/get_recharge_config');
                if (response.data.code === 1) {
                    this.config = response.data.data;
                }
            } catch (error) {
                this.$message.error('获取配置失败');
            }
        },
        
        // 创建充值订单
        async createOrder() {
            if (this.amount < this.config.min_amount) {
                this.$message.error(`充值金额不能少于${this.config.min_amount}元`);
                return;
            }
            
            if (this.amount > this.config.max_amount) {
                this.$message.error(`充值金额不能超过${this.config.max_amount}元`);
                return;
            }
            
            this.loading = true;
            
            try {
                const response = await this.$http.post('/api/recharge/create_order', {
                    amount: this.amount,
                    pay_method: 13,
                    client: 2
                });
                
                if (response.data.code === 1) {
                    this.$message.success('订单创建成功，正在跳转支付...');
                    // 跳转到支付页面
                    window.location.href = response.data.data.pay_url;
                } else {
                    this.$message.error(response.data.msg);
                }
            } catch (error) {
                this.$message.error('创建订单失败');
            } finally {
                this.loading = false;
            }
        }
    }
}
```

## 安全注意事项

1. **HTTPS**: 生产环境必须使用HTTPS
2. **Token验证**: 所有接口都需要有效的JWT Token
3. **金额验证**: 前后端都要验证充值金额范围
4. **订单验证**: 查询订单时验证用户权限
5. **回调验证**: 支付回调必须验证签名

